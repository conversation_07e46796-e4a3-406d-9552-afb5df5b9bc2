/**
 * Image Service
 *
 * This service handles loading images from different sources:
 * 1. Static images from the public directory
 * 2. Images stored as base64 strings in localStorage
 */

// Storage keys
const PRODUCT_IMAGES_KEY = 'sabone-product-images';
const USER_IMAGES_KEY = 'sabone-user-images';

/**
 * Get the actual image source based on the path
 * This function checks if the image is stored in localStorage as base64
 * or if it's a static image in the public directory
 *
 * @param src The image path or URL
 * @returns The resolved image source (base64 data URL or corrected path)
 */
export const getImageSource = (src: string): string => {
  // If src is empty or undefined, return a placeholder
  if (!src) {
    console.warn('Empty image source provided');
    return '/placeholder.svg';
  }

  // Debug the image source
  console.log('Resolving image source:', src);

  // If it's already a data URL, return it as is
  if (src.startsWith('data:')) {
    return src;
  }

  // Check if it's a full URL (external image)
  if (src.startsWith('http://') || src.startsWith('https://')) {
    return src;
  }

  // Normalize the path for consistent lookup
  const normalizedPath = src.startsWith('/') ? src : `/${src}`;
  const _pathWithoutSlash = src.startsWith('/') ? src.substring(1) : src;

  // Check if the image is stored in localStorage
  try {
    // Try product images first
    const productImagesJson = localStorage.getItem(PRODUCT_IMAGES_KEY);
    if (productImagesJson) {
      try {
        const productImages = JSON.parse(productImagesJson);
        if (productImages[normalizedPath] && productImages[normalizedPath].startsWith('data:')) {
          console.log(`Found product image in localStorage: ${normalizedPath}`);
          return productImages[normalizedPath];
        }
      } catch (e) {
        console.error('Error parsing product images JSON from localStorage:', e);
      }
    }

    // Try user images next
    const userImagesJson = localStorage.getItem(USER_IMAGES_KEY);
    if (userImagesJson) {
      try {
        const userImages = JSON.parse(userImagesJson);
        if (userImages[normalizedPath] && userImages[normalizedPath].startsWith('data:')) {
          console.log(`Found user image in localStorage: ${normalizedPath}`);
          return userImages[normalizedPath];
        }
      } catch (e) {
        console.error('Error parsing user images JSON from localStorage:', e);
      }
    }
  } catch (error) {
    console.error('Error accessing localStorage for image:', error);
  }

  // If not found in localStorage, assume it's a static asset.
  // Handle lovable-uploads paths
  if (src.includes('lovable-uploads')) {
    // Extract the filename from the path
    const filename = src.split('/').pop();

    if (!filename) {
      console.error('Invalid image path, no filename found:', src);
      return '/placeholder.svg';
    }

    // Ensure the path is correctly formed for static serving
    // Handle spaces in filenames - don't encode them as they should match the actual filenames
    const correctedPath = `/lovable-uploads/${filename}`;
    console.log(`Correcting lovable-uploads path: ${src} -> ${correctedPath}`);

    // Additional debug logging to help diagnose issues
    console.log(`Image lookup: Checking if file exists at ${correctedPath}`);

    return correctedPath;
  }

  // For all other static images, ensure it has a leading slash
  console.log('Final resolved image path:', normalizedPath);
  return normalizedPath;
};

/**
 * Check if an image exists at the given path
 *
 * @param src The image path to check
 * @returns A promise that resolves to true if the image exists, false otherwise
 */
export const checkImageExists = (src: string): Promise<boolean> => {
  return new Promise((resolve) => {
    // If it's a data URL, it exists
    if (src.startsWith('data:')) {
      resolve(true);
      return;
    }

    // Check if it's in localStorage
    const resolvedSrc = getImageSource(src);
    if (resolvedSrc !== src && resolvedSrc.startsWith('data:')) {
      resolve(true);
      return;
    }

    // Otherwise, check if the image file exists
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = resolvedSrc;
  });
};

/**
 * Preload an image to ensure it's in the browser cache
 *
 * @param src The image path to preload
 * @returns A promise that resolves when the image is loaded
 */
export const preloadImage = (src: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const resolvedSrc = getImageSource(src);
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = resolvedSrc;
  });
};
