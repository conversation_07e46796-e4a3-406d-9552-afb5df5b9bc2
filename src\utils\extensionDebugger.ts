/**
 * Extension Debugger Utility
 * 
 * This utility helps identify and debug issues with browser extensions
 * that might be interfering with your application.
 */

/**
 * Checks if the error is related to a browser extension
 * @param error The error object
 * @returns True if the error is likely from a browser extension
 */
export const isExtensionError = (error: Error): boolean => {
  const errorString = error.toString();
  const stack = error.stack || '';
  
  // Common patterns in extension-related errors
  const extensionPatterns = [
    'contentscript',
    'extension://',
    'chrome-extension://',
    'message port closed',
    'port closed',
    'extension context invalidated'
  ];
  
  return extensionPatterns.some(pattern => 
    errorString.toLowerCase().includes(pattern.toLowerCase()) || 
    stack.toLowerCase().includes(pattern.toLowerCase())
  );
};

/**
 * Wraps message passing functions with error handling
 * @param messageFn The message passing function to wrap
 * @returns A wrapped function with error handling
 */
export const wrapMessageFunction = <T, R>(
  messageFn: (message: T) => Promise<R>
): ((message: T) => Promise<R>) => {
  return async (message: T): Promise<R> => {
    try {
      // Set a timeout to detect if the message port closes prematurely
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Message response timeout - port may have closed'));
        }, 5000); // 5 second timeout
      });
      
      // Race the original message function against the timeout
      return await Promise.race([
        messageFn(message),
        timeoutPromise
      ]) as R;
    } catch (error) {
      console.error('Message passing error:', error);
      if (isExtensionError(error as Error)) {
        console.warn('This appears to be a browser extension error. Try disabling extensions or using incognito mode.');
      }
      throw error;
    }
  };
};

/**
 * Monitors for extension-related errors in the console
 */
export const monitorExtensionErrors = (): () => void => {
  // Save original console.error
  const originalConsoleError = console.error;
  
  // Override console.error to detect extension errors
  console.error = function(...args: any[]) {
    // Call original function
    originalConsoleError.apply(console, args);
    
    // Check if this is an extension error
    const errorString = args.join(' ').toLowerCase();
    if (
      errorString.includes('contentscript.bundle.js') ||
      errorString.includes('message port closed') ||
      errorString.includes('extension')
    ) {
      console.warn(
        '%c Browser Extension Warning ',
        'background: #FFA500; color: white; font-weight: bold',
        'An error from a browser extension was detected. This may affect application performance. Try disabling extensions or using incognito mode.'
      );
    }
  };
  
  // Return a function to restore the original console.error
  return () => {
    console.error = originalConsoleError;
  };
};

/**
 * Creates a safe wrapper for postMessage
 * @param target The target window or frame
 * @returns A wrapped postMessage function with error handling
 */
export const createSafePostMessage = (
  target: Window | MessagePort | ServiceWorker
): (message: any, targetOrigin: string, transfer?: Transferable[]) => void => {
  return (message: any, targetOrigin: string, transfer?: Transferable[]) => {
    try {
      if (target instanceof Window) {
        target.postMessage(message, targetOrigin, transfer);
      } else {
        target.postMessage(message, transfer);
      }
    } catch (error) {
      console.error('Error in postMessage:', error);
      if (isExtensionError(error as Error)) {
        console.warn('This appears to be a browser extension error with postMessage.');
      }
    }
  };
};

/**
 * Initialize all extension debugging tools
 * @returns A cleanup function to remove the debugging tools
 */
export const initExtensionDebugging = (): () => void => {
  const restoreConsole = monitorExtensionErrors();
  
  // Add a global error handler
  const originalOnError = window.onerror;
  window.onerror = function(message, source, lineno, colno, error) {
    if (source?.includes('contentscript.bundle.js') || 
        (error && isExtensionError(error))) {
      console.warn(
        '%c Browser Extension Error Detected ',
        'background: #FFA500; color: white; font-weight: bold',
        'Error from a browser extension was caught. This is not an issue with your application.'
      );
    }
    
    // Call the original handler if it exists
    if (typeof originalOnError === 'function') {
      return originalOnError(message, source, lineno, colno, error);
    }
    return false;
  };
  
  return () => {
    restoreConsole();
    window.onerror = originalOnError;
  };
};
