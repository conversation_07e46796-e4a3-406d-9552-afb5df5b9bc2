import React, { useState, useEffect, useRef, useMemo, memo } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { useIsMobile } from "@/hooks/use-mobile";
import { getImageSource } from "@/services/imageService";
import {
  normalizePath,
  DEFAULT_FALLBACK_IMAGE,
  generateModernImageSources,
  generateBlurPlaceholder
} from '../utils/imageUtils';
import styles from './OptimizedImage.module.css';

interface OptimizedImageProps {
  src: string;
  alt: string;
  fallbackSrc?: string;
  width?: number;
  height?: number;
  className?: string;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
  aspectRatio?: string;
  priority?: boolean;
  objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down";
  sizes?: string;
  quality?: number;
  placeholder?: "blur" | "empty";
  srcSet?: string;
  fetchpriority?: "high" | "low" | "auto";
  decoding?: "sync" | "async" | "auto";
  // Enhanced props for modern image optimization
  enableWebP?: boolean;
  enableAVIF?: boolean;
  responsiveSizes?: Array<{ width: number; quality?: number }>;
  blurDataURL?: string;
  unoptimized?: boolean;
}

/**
 * Enhanced image component with proper error handling, WebP support, and lazy loading
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  fallbackSrc = DEFAULT_FALLBACK_IMAGE,
  width,
  height,
  className = '',
  loading = 'lazy',
  onLoad,
  onError,
  aspectRatio,
  priority = false,
  objectFit = "cover",
  sizes = "(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",
  quality = 85,
  placeholder = "blur",
  srcSet,
  fetchpriority = "auto",
  decoding = "async",
  // Enhanced props with defaults
  enableWebP = true,
  enableAVIF = true,
  responsiveSizes = [
    { width: 640, quality: 75 },
    { width: 768, quality: 80 },
    { width: 1024, quality: 85 },
    { width: 1280, quality: 90 }
  ],
  blurDataURL,
  unoptimized = false,
}: OptimizedImageProps) => {
  const [isLoading, setIsLoading] = useState(!priority);
  const [imgSrc, setImgSrc] = useState<string>(normalizePath(src));
  const [blurSrc, setBlurSrc] = useState("");
  const [isInView, setIsInView] = useState(priority);
  const [isError, setIsError] = useState<boolean>(false);
  const imgRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();

  // Resolve the image source using our image service
  const resolvedSrc = getImageSource(src);

  console.log(`OptimizedImage: Original src: ${src}, Resolved src: ${resolvedSrc}`);

  // Determine if the image is a WebP format
  const isWebP = resolvedSrc.toLowerCase().endsWith('.webp') || resolvedSrc.startsWith('data:image/webp');

  // Generate WebP version URL if not already WebP and not a data URL
  const webpSrc = isWebP || resolvedSrc.startsWith('data:')
    ? resolvedSrc
    : resolvedSrc.replace(/\.(jpe?g|png)$/i, '.webp');

  // Use Intersection Observer for lazy loading
  useEffect(() => {
    if (priority) return; // Skip if priority image

    // Create an observer with a larger rootMargin to start loading earlier
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        // Load images before they enter the viewport for smoother experience
        // Use larger margin on mobile to account for slower connections
        rootMargin: isMobile ? "300px" : "200px",
        threshold: 0.01, // Start loading when even a small part is visible
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [priority, isMobile]);

  // Reset error state if src changes
  useEffect(() => {
    setImgSrc(normalizePath(src));
    setIsError(false);
  }, [src]);

  // Generate modern format sources with responsive sizing
  const sources = useMemo(() => {
    if (imgSrc.startsWith('http') || isError || unoptimized) {
      return null;
    }

    const sourcesData = generateModernImageSources(imgSrc, {
      enableAVIF,
      enableWebP,
      responsiveSizes,
      sizes,
      quality,
    });

    return (
      <>
        {sourcesData.map((source, index) => (
          <source
            key={index}
            srcSet={srcSet && index === sourcesData.length - 1 ? srcSet : source.srcSet}
            type={source.type}
            sizes={source.sizes}
          />
        ))}
      </>
    );
  }, [imgSrc, isError, unoptimized, enableAVIF, enableWebP, responsiveSizes, quality, sizes, srcSet]);

  // Generate or use provided blur placeholder
  useEffect(() => {
    if (!priority && resolvedSrc && isInView) {
      // Use provided blurDataURL if available
      if (blurDataURL && placeholder === 'blur') {
        setBlurSrc(blurDataURL);
        setImgSrc(resolvedSrc);
        return;
      }

      // Generate blur placeholder if needed
      if (placeholder === 'blur') {
        generateBlurPlaceholder(resolvedSrc, {
          width: 20,
          quality: 0.1,
          format: enableWebP ? 'webp' : 'jpeg',
        })
          .then((blurDataURL) => {
            setBlurSrc(blurDataURL);
            setImgSrc(resolvedSrc);
          })
          .catch((error) => {
            console.error("Error generating placeholder:", error);
            setImgSrc(resolvedSrc); // Fallback to original image
          });
      } else {
        // If no blur placeholder is needed, just load the image directly
        setImgSrc(resolvedSrc);
      }
    }
  }, [resolvedSrc, src, priority, onError, isInView, placeholder, blurDataURL, enableWebP]);

  const handleImageLoad = () => {
    setIsLoading(false);
    if (onLoad) onLoad();
  };

  const handleImageError = () => {
    console.error(`Failed to load image. Original src: "${src}", Resolved src: "${resolvedSrc}", WebP src: "${webpSrc}". Falling back to placeholder.`);

    // Optional: A simpler manual check for the resolvedSrc if needed for deeper debugging.
    // const checkImg = new Image();
    // checkImg.onerror = () => console.warn(`Manual check: Image also failed to load directly from "${resolvedSrc}" during error handling.`);
    // checkImg.src = resolvedSrc;

    setIsLoading(false);
    setIsError(true);
    setImgSrc(fallbackSrc);
    if (onError) onError();
  };

  // Determine container classes
  const containerClasses = [
    styles.container,
    width && height ? styles.fixedContainer : styles.responsiveContainer,
    aspectRatio ? styles.containerWithAspectRatio : '',
    styles.containerWithObjectFit,
    styles.optimized,
    className
  ].filter(Boolean).join(' ');

  // Container data attributes for CSS custom properties
  const containerProps = {
    className: containerClasses,
    ref: imgRef,
    'data-width': width,
    'data-height': height,
    'data-aspect-ratio': aspectRatio || (width && height ? `${width} / ${height}` : undefined),
    'data-object-fit': objectFit,
  };

  // Determine image classes
  const imageClasses = [
    styles.image,
    isLoading ? styles.imageLoading : styles.imageLoaded,
    isError ? styles.imageError : '',
    'max-w-full h-auto'
  ].filter(Boolean).join(' ');

  // Determine placeholder classes
  const placeholderClasses = [
    styles.placeholder,
    isLoading ? styles.placeholderVisible : styles.placeholderHidden
  ].filter(Boolean).join(' ');

  const imageProps = {
    src: imgSrc,
    alt,
    width,
    height,
    loading: priority ? 'eager' as const : loading,
    onLoad: handleImageLoad,
    onError: handleImageError,
    className: imageClasses,
    style: { objectFit } as React.CSSProperties,
    fetchpriority,
    decoding,
    sizes: !unoptimized ? sizes : undefined,
  };

  return (
    <div {...containerProps}>
      {isLoading && !blurSrc && (
        <Skeleton className={styles.skeleton} />
      )}

      {blurSrc && (
        <img
          src={blurSrc}
          alt=""
          aria-hidden="true"
          className={placeholderClasses}
        />
      )}

      {sources ? (
        <picture>
          {sources}
          <img {...imageProps} />
        </picture>
      ) : (
        <img {...imageProps} />
      )}
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(OptimizedImage);
