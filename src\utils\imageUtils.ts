/**
 * Utilities for handling image paths, localStorage, and modern image format optimization
 */

/**
 * Default fallback image to use when an image fails to load
 */
export const DEFAULT_FALLBACK_IMAGE = '/placeholder.svg';

// Supported modern image formats
export const MODERN_IMAGE_FORMATS = ['avif', 'webp'] as const;
export type ModernImageFormat = typeof MODERN_IMAGE_FORMATS[number];

// Default responsive breakpoints
export const DEFAULT_RESPONSIVE_SIZES = [
  { width: 640, quality: 75 },
  { width: 768, quality: 80 },
  { width: 1024, quality: 85 },
  { width: 1280, quality: 90 },
  { width: 1920, quality: 95 }
] as const;

export type ResponsiveSize = typeof DEFAULT_RESPONSIVE_SIZES[number];

/**
 * Normalizes an image path to ensure it's properly formatted
 * @param imagePath - The original image path
 * @returns A normalized image path
 */
export const normalizePath = (imagePath: string): string => {
  if (!imagePath) {
    return DEFAULT_FALLBACK_IMAGE;
  }

  // Handle relative paths
  if (imagePath.startsWith('./') || imagePath.startsWith('../')) {
    // Convert to absolute path based on public directory
    return imagePath.replace(/^\.\/|^\.\.\//, '/');
  }

  // Ensure paths start with /
  if (!imagePath.startsWith('/') && !imagePath.startsWith('http')) {
    return `/${imagePath}`;
  }

  return imagePath;
};

/**
 * Safely encodes an image path for storage in localStorage
 * @param imagePath - The image path to encode
 * @returns An encoded image path safe for storage
 */
export const encodeImagePath = (imagePath: string): string => {
  // Normalize the path first
  const normalizedPath = normalizePath(imagePath);

  // Use encodeURIComponent for safe storage
  return encodeURIComponent(normalizedPath);
};

/**
 * Decodes an image path from localStorage
 * @param encodedPath - The encoded image path from storage
 * @returns The decoded image path
 */
export const decodeImagePath = (encodedPath: string): string => {
  if (!encodedPath) {
    return DEFAULT_FALLBACK_IMAGE;
  }

  try {
    return decodeURIComponent(encodedPath);
  } catch (error) {
    console.error('Failed to decode image path:', error);
    return DEFAULT_FALLBACK_IMAGE;
  }
};

/**
 * Checks if an image exists at the given path
 * @param imagePath - The image path to check
 * @returns A promise that resolves to true if the image exists
 */
export const checkImageExists = (imagePath: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();

    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);

    img.src = normalizePath(imagePath);
  });
};

/**
 * Gets a valid image path, checking for existence and falling back if needed
 * @param imagePath - The original image path
 * @param fallbackPath - Optional custom fallback path
 * @returns A promise that resolves to a valid image path
 */
export const getValidImagePath = async (
  imagePath: string,
  fallbackPath: string = DEFAULT_FALLBACK_IMAGE
): Promise<string> => {
  const normalizedPath = normalizePath(imagePath);

  try {
    const exists = await checkImageExists(normalizedPath);
    return exists ? normalizedPath : fallbackPath;
  } catch (error) {
    console.error('Error checking image existence:', error);
    return fallbackPath;
  }
};

/**
 * Safely stores image paths in localStorage to prevent resets
 * @param key - The localStorage key
 * @param imagePaths - Array of image paths to store
 */
export const safelyStoreImagePaths = (key: string, imagePaths: string[]): void => {
  try {
    // Encode all paths before storing
    const encodedPaths = imagePaths.map(encodeImagePath);
    localStorage.setItem(key, JSON.stringify(encodedPaths));
  } catch (error) {
    console.error(`Failed to store image paths in localStorage (${key}):`, error);
  }
};

/**
 * Safely retrieves image paths from localStorage
 * @param key - The localStorage key
 * @returns Array of decoded image paths or empty array if not found
 */
export const safelyRetrieveImagePaths = (key: string): string[] => {
  try {
    const storedValue = localStorage.getItem(key);

    if (!storedValue) {
      return [];
    }

    const encodedPaths = JSON.parse(storedValue);
    return Array.isArray(encodedPaths)
      ? encodedPaths.map(decodeImagePath)
      : [];
  } catch (error) {
    console.error(`Failed to retrieve image paths from localStorage (${key}):`, error);
    return [];
  }
};

/**
 * Generates a responsive srcSet string for different image sizes
 * @param basePath - The base image path without extension
 * @param format - The image format (webp, avif, jpg, png)
 * @param sizes - Array of responsive sizes
 * @returns A srcSet string for responsive images
 */
export const generateResponsiveSrcSet = (
  basePath: string,
  format: string,
  sizes: Array<{ width: number; quality?: number }> = [...DEFAULT_RESPONSIVE_SIZES]
): string => {
  // Ensure we have valid sizes and generate proper srcSet with width descriptors
  return sizes
    .filter(({ width }) => width && width > 0) // Filter out invalid widths
    .map(({ width }) => {
      // Ensure the width descriptor is properly formatted
      const imageUrl = `${basePath}-${width}w.${format}`;
      return `${imageUrl} ${width}w`;
    })
    .join(', ');
};

/**
 * Generates modern format sources for a picture element
 * @param imagePath - The original image path
 * @param options - Configuration options
 * @returns Array of source elements data
 */
export const generateModernImageSources = (
  imagePath: string,
  options: {
    enableAVIF?: boolean;
    enableWebP?: boolean;
    responsiveSizes?: Array<{ width: number; quality?: number }>;
    sizes?: string;
    quality?: number;
  } = {}
) => {
  const {
    enableAVIF = true,
    enableWebP = true,
    responsiveSizes = DEFAULT_RESPONSIVE_SIZES,
    sizes = "(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",
  } = options;

  // Extract path without extension
  const basePath = imagePath.replace(/\.\w+$/, '');
  const originalExt = imagePath.split('.').pop() || 'jpg';

  const sources = [];

  // Convert readonly array to mutable array
  const sizesArray = Array.isArray(responsiveSizes) ? [...responsiveSizes] : [...DEFAULT_RESPONSIVE_SIZES];

  // AVIF source (best compression)
  if (enableAVIF) {
    sources.push({
      srcSet: generateResponsiveSrcSet(basePath, 'avif', sizesArray),
      type: 'image/avif',
      sizes,
    });
  }

  // WebP source (good compression, wide support)
  if (enableWebP) {
    sources.push({
      srcSet: generateResponsiveSrcSet(basePath, 'webp', sizesArray),
      type: 'image/webp',
      sizes,
    });
  }

  // Fallback source (original format)
  sources.push({
    srcSet: generateResponsiveSrcSet(basePath, originalExt, sizesArray),
    type: originalExt === 'png' ? 'image/png' : 'image/jpeg',
    sizes,
  });

  return sources;
};

/**
 * Checks if the browser supports a specific image format
 * @param format - The image format to check
 * @returns Promise that resolves to true if supported
 */
export const checkImageFormatSupport = (format: ModernImageFormat): Promise<boolean> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;

    try {
      const dataURL = canvas.toDataURL(`image/${format}`);
      resolve(dataURL.startsWith(`data:image/${format}`));
    } catch {
      resolve(false);
    }
  });
};

/**
 * Generates a low-quality placeholder image data URL
 * @param imagePath - The original image path
 * @param options - Configuration options
 * @returns Promise that resolves to a blur placeholder data URL
 */
export const generateBlurPlaceholder = (
  imagePath: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpeg';
  } = {}
): Promise<string> => {
  const { width = 20, height: _height = 20, quality = 0.1, format = 'webp' } = options;

  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Could not get canvas context'));
          return;
        }

        // Calculate aspect ratio
        const aspectRatio = img.width / img.height;
        canvas.width = width;
        canvas.height = Math.round(width / aspectRatio);

        // Draw the image at a tiny size
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // Generate data URL
        try {
          const dataURL = canvas.toDataURL(`image/${format}`, quality);
          resolve(dataURL);
        } catch {
          // Fallback to JPEG if format not supported
          const fallbackDataURL = canvas.toDataURL('image/jpeg', quality);
          resolve(fallbackDataURL);
        }
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error(`Failed to load image: ${imagePath}`));
    };

    img.src = imagePath;
  });
};